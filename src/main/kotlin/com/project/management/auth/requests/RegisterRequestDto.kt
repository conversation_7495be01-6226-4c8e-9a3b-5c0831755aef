package com.project.management.auth.requests

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.project.management.users.UserPostRequest
import com.project.management.organization.OrganizationPostRequest

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RegisterRequestDto (
    val user: UserPostRequest,
    val organization: OrganizationPostRequest,
)