package com.project.management.organization

import com.project.management.users.User
import java.math.BigDecimal

fun OrganizationPostRequest.toModel(performer: User): Organization {
    return Organization(
        address = "",
        description = description,
        email = "${phoneNumber}@a.c",
        logoUrl = "",
        name = name,
        organizationCode = phoneNumber,
        phoneNumber = phoneNumber,
        secondaryPhoneNumber = "",
        website = "",
        photoUrl = "",
        capital = 0.0.toBigDecimal(),
        availableBalance = 0.0.toBigDecimal(),
        totalExpenses = 0.0.toBigDecimal(),
        totalPaidExpenses = 0.0.toBigDecimal(),
        totalIncomes = 0.0.toBigDecimal(),
        totalPaidIncomes = 0.0.toBigDecimal(),
        ownerId = performer.id,
        createdBy = performer.id,
        updatedBy = performer.id,
    )
}

fun Organization.toResponse(
    expenses: BigDecimal,
    incomes: BigDecimal,
    paidIncomes: BigDecimal,
    totalCapital: BigDecimal,
): OrganizationPostResponse {
    return OrganizationPostResponse(
        id = id,
        address = address,
        description = description,
        name = name,
        email = email,
        logoUrl = logoUrl,
        website = website,
        phoneNumber = phoneNumber,
        secondaryPhoneNumber = secondaryPhoneNumber,
        capital = capital,
        totalCapital = totalCapital,
        expenses = expenses,
        incomes = incomes,
        paidIncomes = paidIncomes,
        organizationCode = organizationCode,
        createdBy = createdBy,
        updatedBy = updatedBy,
        createdAt = createdAt.toString(),
        updatedAt = updatedAt.toString(),
        ownerId = ownerId
    )
}
