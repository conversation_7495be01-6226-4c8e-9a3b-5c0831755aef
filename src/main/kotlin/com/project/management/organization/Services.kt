package com.project.management.organization

import com.project.management.beneficiaries.services.BeneficiaryTransactionQueryService
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.validate
import com.project.management.customers.CustomerTransactionService
import com.project.management.users.UsersService
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class OrganizationService(
    private val users: UsersService,
    private val expenses: BeneficiaryTransactionQueryService,
    private val incomes: CustomerTransactionService,
    private val currentUser: CurrentUserConfig,
) {

    fun getById(id: Long): OrganizationPostResponse {
        val user = currentUser.getCurrentUser()
        val organization = OrganizationRepository.Query.getById(user).validate()
        return getOrganizationResponse(organization)
    }

    fun getUserOrganization(): OrganizationPostResponse {
        val user = currentUser.getCurrentUser()
        return getById(user.organizationId)
    }

    @Transactional
    private fun getOrganizationResponse(organization: Organization): OrganizationPostResponse {
        val incomes = incomes.getAll().sumOf { it.amount }
        val paidIncomes = this.incomes.getAll().sumOf { it.amountPaid }
        val expenses = expenses.getAll().sumOf { it.amountPaid }
        val balance = users.getAll().sumOf { it.balance.negate() }
        return organization.toResponse(
            expenses = expenses,
            incomes = incomes,
            paidIncomes = paidIncomes,
            totalCapital = (organization.capital + paidIncomes + balance - expenses),
        )
    }
}

@Service
class CapitalTransactionsService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<CapitalTransaction> {
        val user = currentUser.getCurrentUser()
        val capitalTransactions = CapitalTransactionRepository.Query.getAll(user)

        return capitalTransactions
    }
}