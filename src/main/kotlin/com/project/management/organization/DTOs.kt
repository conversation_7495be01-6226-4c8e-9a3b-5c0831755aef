package com.project.management.organization

import java.math.BigDecimal

data class OrganizationPostRequest(
    val description: String,
    val name: String,
    val phoneNumber: String,
)

data class OrganizationPostResponse(
    val id: Long,
    val address: String,
    val description: String,
    val name: String,
    val email: String,
    val logoUrl: String?,
    val website: String?,
    val phoneNumber: String,
    val secondaryPhoneNumber: String?,
    val capital: BigDecimal,
    val totalCapital: BigDecimal,
    val expenses: BigDecimal,
    val incomes: BigDecimal,
    val paidIncomes: BigDecimal,
    val organizationCode: String,
    val createdBy: Long,
    val updatedBy: Long,
    val createdAt: String,
    val updatedAt: String,
    val ownerId: Long
)

data class CapitalTransactionRequest(
    val amount: Double,
    val description: String,
    val transactionDate: String
)
