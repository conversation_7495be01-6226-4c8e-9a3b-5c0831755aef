package com.project.management.organization

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import kotlin.jvm.optionals.getOrNull

object OrganizationRepository {
    private val repo: SpringOrganizationRepository by autowired()

    object Query {
        fun getById(organizationId: OrganizationId): Organization? = filterDeleted {
            repo.findById(organizationId.value).getOrNull()
        }

        fun sumAllCapital(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumCapitalByOrganizationId(organizationId.value)
        }

        fun sumAllAvailableBalance(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAvailableBalanceByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(organization: Organization): Organization {
            return repo.save(organization)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(ORGANIZATION_DELETED_FILTER, block)
}

object CapitalTransactionRepository {
    private val repo: SpringCapitalTransactionRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<CapitalTransaction> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): CapitalTransaction? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumAllAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(capitalTransaction: CapitalTransaction): CapitalTransaction {
            return repo.save(capitalTransaction)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(CAPITAL_TRANSACTION_DELETED_FILTER, block)
}

@Repository
private interface SpringOrganizationRepository : JpaRepository<Organization, Long> {
    // SUM queries for organization aggregators
    @Query(
        value = "SELECT COALESCE(SUM(capital), 0) FROM organizations WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumCapitalByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(available_balance), 0) FROM organizations WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAvailableBalanceByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringCapitalTransactionRepository : JpaRepository<CapitalTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<CapitalTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): CapitalTransaction?

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM capital_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal
}
