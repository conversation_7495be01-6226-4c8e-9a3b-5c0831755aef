package com.project.management.organization

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object OrganizationRepository {
    private val repo: SpringOrganizationRepository by autowired()

    object Query {
        fun getById(organizationId: OrganizationId): Organization? = filterDeleted {
            repo.findByIdAndOrganizationId(organizationId.value)
        }

        fun sumAllCapital(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumCapitalByOrganizationId(organizationId.value)
        }

        fun sumAllAvailableBalance(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAvailableBalanceByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(organization: Organization): Organization {
            return repo.save(organization)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(ORGANIZATION_DELETED_FILTER, block)
}

@Repository
private interface SpringOrganizationRepository : JpaRepository<Organization, Long> {
    fun findByIdAndOrganizationId(id: Long): Organization?

    // SUM queries for organization aggregators
    @Query(
        value = "SELECT COALESCE(SUM(capital), 0) FROM organizations WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumCapitalByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(available_balance), 0) FROM organizations WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAvailableBalanceByOrganizationId(organizationId: Long): BigDecimal
}
