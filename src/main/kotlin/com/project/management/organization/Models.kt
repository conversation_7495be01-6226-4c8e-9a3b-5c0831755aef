package com.project.management.organization

import com.project.management.common.entity.ServerEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.math.BigDecimal

const val ORGANIZATION_DELETED_FILTER = "organization_deleted_filter"

@Entity
@Table(name = "organizations")
@FilterDef(name = ORGANIZATION_DELETED_FILTER, parameters = [])
@Filter(name = ORGANIZATION_DELETED_FILTER, condition = "deleted is NULL")
class Organization(
    var address: String,
    var description: String,
    var email: String,
    var logoUrl: String?,
    var name: String,
    var organizationCode: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var website: String,
    var photoUrl: String,

    val ownerId: Long,

    var capital: BigDecimal,
    var availableBalance: BigDecimal,

    var totalExpenses: BigDecimal,
    var totalPaidExpenses: BigDecimal,
    var totalIncomes: BigDecimal,
    var totalPaidIncomes: BigDecimal,

    override val createdBy: Long,
    override var updatedBy: Long,
) : ServerEntity() {
    override val organizationId: Long get() = this.id
}