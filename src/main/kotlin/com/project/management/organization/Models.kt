package com.project.management.organization

import com.project.management.common.entity.ServerEntity
import com.project.management.users.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import org.hibernate.annotations.SQLRestriction
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

const val ORGANIZATION_DELETED_FILTER = "organization_deleted_filter"
const val CAPITAL_TRANSACTION_DELETED_FILTER = "capital_transaction_deleted_filter"

@Entity
@Table(name = "organizations")
@FilterDef(name = ORGANIZATION_DELETED_FILTER, parameters = [])
@Filter(name = ORGANIZATION_DELETED_FILTER, condition = "deleted is NULL")
class Organization(
    var address: String,
    var description: String,
    var email: String,
    var logoUrl: String?,
    var name: String,
    var organizationCode: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var website: String,
    var photoUrl: String,

    val ownerId: Long,

    var capital: BigDecimal,
    var availableBalance: BigDecimal,

    var totalExpenses: BigDecimal,
    var totalPaidExpenses: BigDecimal,
    var totalIncomes: BigDecimal,
    var totalPaidIncomes: BigDecimal,

    override val createdBy: Long,
    override var updatedBy: Long,
) : ServerEntity() {
    override val organizationId: Long get() = this.id
}

@Entity
@Table(name = "capital_transactions")
@FilterDef(name = CAPITAL_TRANSACTION_DELETED_FILTER, parameters = [])
@Filter(name = CAPITAL_TRANSACTION_DELETED_FILTER, condition = "deleted is NULL")
class CapitalTransaction(
    val organizationId: Long,

    var amount: BigDecimal,
    var description: String,
    var transactionDate: ZonedDateTime,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "created_by")
    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
) {
    @PreUpdate
    fun setLastUpdate() {
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}