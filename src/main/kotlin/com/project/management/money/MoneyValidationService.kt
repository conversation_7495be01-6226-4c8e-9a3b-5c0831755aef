package com.project.management.money

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.customers.CustomerRepository
import com.project.management.customers.CustomerTransactionRepository
import com.project.management.organization.Organization
import com.project.management.organization.OrganizationRepository
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.UserRepository
import com.project.management.users.BalanceTransactionService
import org.springframework.stereotype.Service

@Service
class MoneyValidationService(
    private val beneficiaryTransactionValidator: BeneficiaryTransactionValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val projectValidator: ProjectValidator,
    private val usersTransactions: BalanceTransactionService,

    ) {

    fun validate(organizationId: Long) {
        val organization = OrganizationRepository.Query
            .getById(OrganizationId(organizationId)).validate()

        validateExpenses(organization)
        validateIncomes(organization)
        validateOrganizationBalance(organization)
    }

    private fun validateExpenses(organization: Organization) {
        val users = usersTransactions.getAll()
        val userExpenses = users.filter { it.transactionTag.isProjectExpense() }.sumOf { it.amount }

        val projectsAmount = projectValidator.sumTotalExpensesByOrganizationId(organization.id)
        val projectsPaid = projectValidator.sumTotalPaidExpensesByOrganizationId(organization.id)

        val projectsTransactionsAmount = projectExpenseValidator
            .sumAmountByOrganizationId(organization.id)
        val projectsTransactionsPaid = projectExpenseValidator
            .sumAmountPaidByOrganizationId(organization.id)

        val beneficiariesAmount = BeneficiaryRepository.Query
            .sumAllBalance(OrganizationId(organization.id)) + beneficiaryTransactionValidator
            .sumGeneralAmountByOrganizationId(organization.id)
        val beneficiariesPaid = BeneficiaryRepository.Query
            .sumAllPaid(OrganizationId(organization.id)) + beneficiaryTransactionValidator
            .sumGeneralAmountPaidByOrganizationId(organization.id)

        val beneficiaryTransactionsAmount =
            beneficiaryTransactionValidator.sumAmountByOrganizationId(organization.id)
        val beneficiaryTransactionsPaid =
            beneficiaryTransactionValidator.sumAmountPaidByOrganizationId(organization.id)

        if (userExpenses.negate().compareTo(projectsTransactionsPaid) != 0) {
            throw IllegalStateException("User expenses do not match project expenses paid: ${userExpenses.negate()} != $projectsTransactionsPaid")
        }

        if (projectsTransactionsAmount.compareTo(beneficiaryTransactionsAmount) != 0) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiaryTransactionsAmount")
        }

        if (projectsTransactionsPaid.compareTo(beneficiaryTransactionsPaid) != 0) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiaryTransactionsPaid")
        }

        if (projectsTransactionsAmount.compareTo(projectsAmount) != 0) {
            throw IllegalStateException("Projects amount does not match project expenses amount: $projectsTransactionsAmount != $projectsAmount")
        }

        if (projectsTransactionsPaid.compareTo(projectsPaid) != 0) {
            throw IllegalStateException("Projects paid does not match project expenses paid: $projectsTransactionsPaid != $projectsPaid")
        }

        if (projectsTransactionsAmount.compareTo(beneficiariesAmount) != 0) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiariesAmount")
        }

        if (projectsTransactionsPaid.compareTo(beneficiariesPaid) != 0) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiariesPaid")
        }

        if (projectsTransactionsPaid.compareTo(organization.totalPaidExpenses) != 0) {
            throw IllegalStateException("Projects paid does not match organization paid expenses: $projectsTransactionsPaid != ${organization.totalPaidExpenses}")
        }

        if (projectsTransactionsAmount.compareTo(organization.totalExpenses) != 0) {
            throw IllegalStateException("Projects amount does not match organization expenses: $projectsTransactionsAmount != ${organization.totalExpenses}")
        }
    }

    private fun validateIncomes(organization: Organization) {
        val projectsAmount = projectValidator.sumTotalIncomesByOrganizationId(organization.id)
        val projectsPaid = projectValidator.sumTotalPaidIncomesByOrganizationId(organization.id)

        val projectTransactionsAmount =
            projectIncomeValidator.sumAmountByOrganizationId(organization.id)
        val projectTransactionsPaid =
            projectIncomeValidator.sumAmountPaidByOrganizationId(organization.id)

        val customersAmount = CustomerRepository.Query.sumAllBalance(organization)
        val customersPaid = CustomerRepository.Query.sumAllPaid(organization)

        val customerTransactionsAmount = CustomerTransactionRepository.Query
            .sumAllAmount(organization)
        val customerTransactionsPaid = CustomerTransactionRepository.Query
            .sumAllAmountPaid(organization)

        if (projectTransactionsAmount.compareTo(customerTransactionsAmount) != 0) {
            throw IllegalStateException("Projects amount does not match customers amount: $projectTransactionsAmount != $customerTransactionsAmount")
        }

        if (projectTransactionsPaid.compareTo(customerTransactionsPaid) != 0) {
            throw IllegalStateException("Projects paid does not match customers paid: $projectTransactionsPaid != $customerTransactionsPaid")
        }

        if (projectTransactionsAmount.compareTo(projectsAmount) != 0) {
            throw IllegalStateException("Projects amount does not match project incomes amount: $projectTransactionsAmount != $projectsAmount")
        }

        if (projectTransactionsPaid.compareTo(projectsPaid) != 0) {
            throw IllegalStateException("Projects paid does not match project incomes paid: $projectTransactionsPaid != $projectsPaid")
        }

        if (projectTransactionsAmount.compareTo(customersAmount) != 0) {
            throw IllegalStateException("Projects amount does not match customers amount: $projectTransactionsAmount != $customersAmount")
        }

        if (projectTransactionsPaid.compareTo(customersPaid) != 0) {
            throw IllegalStateException("Projects paid does not match customers paid: $projectTransactionsPaid != $customersPaid")
        }

        if (projectTransactionsPaid.compareTo(organization.totalPaidIncomes) != 0) {
            throw IllegalStateException("Projects paid does not match organization paid incomes: $projectTransactionsPaid != ${organization.totalPaidIncomes}")
        }

        if (projectTransactionsAmount.compareTo(organization.totalIncomes) != 0) {
            throw IllegalStateException("Projects amount does not match organization incomes: $projectTransactionsAmount != ${organization.totalIncomes}")
        }
    }

    private fun validateOrganizationBalance(organization: Organization) {
        val users = UserRepository.Query.sumAllBalance(OrganizationId(organization.id)).negate()
        val calculated =
            (organization.capital + organization.totalPaidIncomes + users) - (organization.totalPaidExpenses)

        if (organization.availableBalance.compareTo(calculated) != 0) {
            throw IllegalStateException("Organization available balance does not match calculated balance: ${organization.availableBalance} != $calculated")
        }
    }
}