package com.project.management.money.organization

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.validate
import com.project.management.money.MoneyValidationService
import com.project.management.organization.CapitalTransaction
import com.project.management.organization.Organization
import com.project.management.organization.CapitalTransactionRepository
import com.project.management.organization.OrganizationRepository
import com.project.management.organization.toEntity
import com.project.management.organization.CapitalTransactionRequest
import com.project.management.users.User
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.ZonedDateTime

@Service
class OrganizationCapitalMoneyMutateService(
    private val organizationBalanceMutateService: OrganizationBalanceMutateService,
    private val currentUser: CurrentUserConfig,
    private val money: MoneyValidationService
) {

    @Transactional
    fun addCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val user = currentUser.getCurrentUser()
        val transaction = createCapitalTransaction(performer = user, request = request)

        increaseCapital(request.amount.toBigDecimal())

        return transaction
    }

    @Transactional
    fun removeCapital(request: CapitalTransactionRequest): CapitalTransaction {
        val user = currentUser.getCurrentUser()
        val negativeRequest = CapitalTransactionRequest(
            amount = request.amount * -1,
            description = request.description,
            transactionDate = request.transactionDate
        )
        val transaction = createCapitalTransaction(
            performer = user, request = negativeRequest
        )

        decreaseCapital(request.amount.toBigDecimal())

        return transaction
    }

    @Transactional
    fun modifyCapital(
        organizationId: Long,
        amount: BigDecimal,
        description: String,
        transactionDate: ZonedDateTime = ZonedDateTime.now()
    ): CapitalTransaction {
        TODO()
        money.validate(organizationId)
    }

    private fun increaseCapital(amount: BigDecimal): Organization {
        val performer = currentUser.getCurrentUser()
        val organization = OrganizationRepository.Query.getById(performer).validate()

        val updated = organizationBalanceMutateService.increaseAvailableBalance(amount)
        updated.capital = organization.capital.add(amount)
        updated.updatedBy = performer.id

        return OrganizationRepository.Mutate.save(updated)
    }

    private fun decreaseCapital(amount: BigDecimal): Organization {
        val performer = currentUser.getCurrentUser()
        val organization = OrganizationRepository.Query.getById(performer).validate()

        val updated = organizationBalanceMutateService.decreaseAvailableBalance(amount)
        updated.capital = organization.capital.minus(amount)
        updated.updatedBy = performer.id

        return OrganizationRepository.Mutate.save(updated)
    }

    private fun modifyCapital(oldAmount: BigDecimal, newAmount: BigDecimal): Organization {
        val performer = currentUser.getCurrentUser()
        val organization = OrganizationRepository.Query.getById(performer).validate()

        val updated = organizationBalanceMutateService.modifyAvailableBalance(oldAmount, newAmount)
        updated.capital = organization.capital.subtract(oldAmount).add(newAmount)
        updated.updatedBy = performer.id

        return OrganizationRepository.Mutate.save(updated)
    }

    private fun createCapitalTransaction(
        request: CapitalTransactionRequest,
        performer: User
    ): CapitalTransaction {
        OrganizationRepository.Query.getById(performer).validate()

        val transaction = request.toEntity(performer = performer)

        return CapitalTransactionRepository.Mutate.save(transaction)
    }
}
