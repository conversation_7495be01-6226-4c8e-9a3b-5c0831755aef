package com.project.management.money.customer

import com.project.management.customers.toCustomerTransaction
import com.project.management.customers.toEntity
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import com.project.management.customers.CustomerRepository
import com.project.management.customers.CustomerTransactionRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.projects.requests.PostRequestProjectIncome
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class CustomerMoneyMutateService(
    private val currentUser: CurrentUserConfig,
) {

    @Transactional
    fun projectIncomeAdd(
        request: PostRequestProjectIncome,
        projectId: Long
    ): CustomerTransaction {
        increaseAccumulators(
            customerId = request.customerId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createTransaction(request, projectId)
    }

    @Transactional
    fun projectIncomeDelete(
        customerId: Long,
        transactionId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        deleteTransaction(transactionId = transactionId)

        return decreaseAccumulators(
            customerId = customerId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    @Transactional
    fun projectIncomeModify(
        transaction: CustomerTransaction,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): Customer {
        val amount = transaction.amount.negate() + newAmount
        val amountPaid = transaction.amountPaid.negate() + newAmountPaid

        updateTransaction(transaction = transaction, amount = newAmount, amountPaid = newAmountPaid)

        return increaseAccumulators(
            customerId = transaction.customerId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    private fun increaseAccumulators(
        customerId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        val currentUser = currentUser.getCurrentUser()
        var customer = CustomerRepository.Query
            .getById(OrganizationId(currentUser.organizationId), customerId).validate()

        customer.balanceAccumulator = customer.balanceAccumulator.plus(amount)
        customer.paidAccumulator = customer.paidAccumulator.plus(amountPaid)
        customer.updatedBy = currentUser.id!!

        return CustomerRepository.Mutate.save(customer)
    }

    private fun decreaseAccumulators(
        customerId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Customer {
        val currentUser = currentUser.getCurrentUser()
        var customer = CustomerRepository.Query
            .getById(OrganizationId(currentUser.organizationId), customerId).validate()

        // Update properties manually since we moved from data class to regular class
        customer.balanceAccumulator = customer.balanceAccumulator.minus(amount)
        customer.paidAccumulator = customer.paidAccumulator.minus(amountPaid)
        customer.updatedBy = currentUser.id!!

        return CustomerRepository.Mutate.save(customer)
    }

    private fun createTransaction(
        request: PostRequestProjectIncome,
        projectId: Long
    ): CustomerTransaction {
        val currentUser = currentUser.getCurrentUser()
        val entity = request
            .toCustomerTransaction(projectId)
            .toEntity(request.customerId, currentUser)

        return CustomerTransactionRepository.Mutate.save(entity)
    }

    private fun updateTransaction(
        transaction: CustomerTransaction,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): CustomerTransaction {
        val currentUser = currentUser.getCurrentUser()

        val updatedTransaction = transaction

        updatedTransaction.amount = amount
        updatedTransaction.amountPaid = amountPaid
        updatedTransaction.updatedBy = currentUser.id!!


        return CustomerTransactionRepository.Mutate.save(updatedTransaction)
    }

    private fun deleteTransaction(transactionId: Long) {
        val performer = currentUser.getCurrentUser()
        CustomerTransactionRepository.Mutate.deleteById(
            id = transactionId,
            organizationId = OrganizationId(performer.organizationId),
            updatedBy = performer.id!!
        )
    }
}
