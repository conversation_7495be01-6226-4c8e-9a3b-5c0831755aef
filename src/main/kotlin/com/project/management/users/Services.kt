package com.project.management.users

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.projects.models.UserProjectAccessEntity
import com.project.management.projects.repositories.ProjectAccessRepository
import com.project.management.auth.validators.AuthValidator
import com.project.management.common.entity.validate
import com.project.management.organization.OrganizationRepository
import jakarta.persistence.EntityManager
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service

@Service
class UsersService(
    private val projectAccessRepository: ProjectAccessRepository,
    private val currentUser: CurrentUserConfig,
    private val authValidator: AuthValidator,
    private val passwordEncoder: PasswordEncoder,
    private val userMoneyMutateService: UserMoneyMutateService
) {

    @Autowired
    private lateinit var entityManager: EntityManager

    fun getAll(): List<User> {
        val user = currentUser.getCurrentUser()

        return UserRepository.Query.getAll(user)
    }

    fun getUser(userId: Long): User {
        val user = currentUser.getCurrentUser()
        return UserRepository.Query.getById(user, userId).validate()
    }

    fun getAllProjectAccess(projectId: Long): List<UserProjectAccessEntity> {
        val user = currentUser.getCurrentUser()
        val projectAccess = projectAccessRepository.findAllByOrganizationIdAndProjectId(
            organizationId = user.organizationId,
            projectId = projectId
        )
        return UserRepository.Query.getAllByIds(projectAccess.map { it.userId }).map { user ->
            UserProjectAccessEntity(
                user = user,
                projectAccess = projectAccess.find { it.userId == user.id }!!
            )
        }
    }

    fun getProjectAccessByUserIdAndProjectId(
        userId: Long,
        projectId: Long
    ): UserProjectAccessEntity {
        val current = currentUser.getCurrentUser()
        val user = UserRepository.Query.getById(current, userId).validate()
        val projectAccess = projectAccessRepository.findByUserIdAndProjectId(user.id, projectId)
            ?: throw BusinessException.NotFoundException(message = "Project access does not exist.")
        return UserProjectAccessEntity(
            user = user,
            projectAccess = projectAccess
        )
    }

    fun create(userDto: UserPostRequest): User {
        val currentUser = currentUser.getCurrentUser()
        authValidator.isPasswordMatch(userDto.password, userDto.confirmPassword)
        val validation = runCatching {
            UserRepository.Query.getByUsername(
                organizationCode = currentUser.organizationCode,
                username = userDto.username
            )
        }
        if (validation.isSuccess) {
            throw BusinessException.ConflictException(message = "User with username ${userDto.username} already exists.")
        }
        val user = userDto.toEntity(
            organizationId = currentUser.organizationId,
            createdBy = currentUser.id,
            updatedBy = currentUser.id,
            organizationCode = currentUser.organizationCode,
            isAdmin = userDto.isAdmin
        )
        user.password = passwordEncoder.encode(user.password)
        user.organizationCode = currentUser.organizationCode
        UserRepository.Mutate.save(user)
        return user
    }

    @Transactional
    fun delete(userId: Long) {
        val currentUser = currentUser.getCurrentUser()
        val user = UserRepository.Query.getById(currentUser, userId).validate()
        val organization = OrganizationRepository.Query.getById(user).validate()
        if (!currentUser.isAdmin) throw BusinessException.ForbiddenException(message = "You do not have permission to delete this user.")
        if (user.balance.toDouble() != 0.0) throw BusinessException.BadRequestException(message = "Cannot delete user with balance not equal to zero (${user.balance}).")
        if (user.id == organization.ownerId) throw BusinessException.BadRequestException(message = "Cannot delete organization owner.")

        UserRepository.Mutate.deleteById(
            id = user.id,
            organizationId = user,
            updatedBy = currentUser.id
        )
    }
}

@Service
class BalanceTransactionService(
    private val currentUser: CurrentUserConfig
) {

    fun getAll(): List<BalanceTransaction> {
        val user = currentUser.getCurrentUser()
        return BalanceTransactionRepository.Query.getAll(user)
    }

    fun getByUserId(userId: Long): List<BalanceTransaction> {
        val user = currentUser.getCurrentUser()
        return BalanceTransactionRepository.Query.getAllByUserId(user, userId)
    }

}